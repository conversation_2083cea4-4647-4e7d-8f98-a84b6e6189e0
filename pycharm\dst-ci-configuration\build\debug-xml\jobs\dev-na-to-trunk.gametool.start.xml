<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job@1505.vea_4b_20a_4a_495">
  <actions/>
  <description>Sync dev-na-to-trunk code and deploy a binary build.</description>
  <logRotator class="hudson.tasks.LogRotator">
    <daysToKeep>7</daysToKeep>
    <numToKeep>100</numToKeep>
    <artifactDaysToKeep>-1</artifactDaysToKeep>
    <artifactNumToKeep>-1</artifactNumToKeep>
    <removeLastBuild>false</removeLastBuild>
  </logRotator>
  <keepDependencies>false</keepDependencies>
  <properties>
    <EnvInjectJobProperty plugin="envinject@2.926.v69c9b_3896a_96">
      <info>
        <propertiesContent>BRANCH_NAME=dev-na-to-trunk
CODE_BRANCH=dev-na-to-trunk
CODE_FOLDER=stage
PROJECT_NAME=bct
NON_VIRTUAL_CODE_BRANCH=
NON_VIRTUAL_CODE_FOLDER=</propertiesContent>
        <loadFilesFromMaster>false</loadFilesFromMaster>
      </info>
      <on>true</on>
      <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
      <keepBuildVariables>true</keepBuildVariables>
      <overrideBuildParameters>false</overrideBuildParameters>
      <contributors/>
    </EnvInjectJobProperty>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
        <hudson.model.StringParameterDefinition>
          <name>CODE_CHANGELIST</name>
          <description>Specifies code changelist to sync.</description>
          <trim>true</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.BooleanParameterDefinition>
          <name>CLEAN_LOCAL</name>
          <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
          <defaultValue>false</defaultValue>
        </hudson.model.BooleanParameterDefinition>
        <hudson.model.BooleanParameterDefinition>
          <name>SUBMIT</name>
          <description>Submit the result to perforce. Uncheck this if you want dry-run</description>
          <defaultValue>true</defaultValue>
        </hudson.model.BooleanParameterDefinition>
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
    <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty/>
    <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty/>
    <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
      <triggers>
        <hudson.triggers.SCMTrigger>
          <spec>H/2 * * * 1-6
H/2 6-23 * * 7</spec>
          <ignorePostCommitHooks>false</ignorePostCommitHooks>
        </hudson.triggers.SCMTrigger>
      </triggers>
    </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    <com.sonyericsson.jenkins.plugins.bfa.model.ScannerJobProperty plugin="build-failure-analyzer@2.5.4">
      <doNotScan>false</doNotScan>
    </com.sonyericsson.jenkins.plugins.bfa.model.ScannerJobProperty>
  </properties>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition" plugin="workflow-cps@4018.vf02e01888da_f">
    <script>package scripts.schedulers.gametool

import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJenkins
import com.ea.lib.LibPerforce
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * GametoolScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage(&apos;Get changelist from Perforce&apos;) {
            steps {
                script {
                    String p4CodeRoot = LibCommonNonCps.get_setting_value(branchInfo, [], &apos;p4_code_root&apos;, &apos;&apos;, project)
                    String p4CodeCredentials = LibCommonNonCps.get_setting_value(branchInfo, [], &apos;p4_code_creds&apos;, &apos;&apos;, project)
                    boolean isTestEnvironment = env.PRODUCTION == &apos;False&apos;
                    List&lt;String&gt; gametools = []
                    branchInfo.gametool_settings.gametools.each { String gametool, Map value -&gt;
                        gametools &lt;&lt; gametool
                    }
                    LibPerforce libPerforce = new LibPerforce(this, project.short_name, gametools,
                        env.CODE_FOLDER, env.CODE_BRANCH, p4CodeRoot, p4CodeCredentials, env.JOB_NAME,
                        isTestEnvironment, env.NON_VIRTUAL_CODE_FOLDER, env.NON_VIRTUAL_CODE_BRANCH)
                    libPerforce.setPollScmTriggers()
                    String codeChangelist = params.CODE_CHANGELIST ?: env.P4_CHANGELIST
                    List&lt;String&gt; modifiedTools = libPerforce.getModifiedTools(codeChangelist)
                    Map&lt;String, String&gt; injectMap = [
                        CODE_CHANGELIST   : codeChangelist,
                        GAMETOOLS_TO_BUILD: modifiedTools.inspect(),
                    ]
                    EnvInject(currentBuild, injectMap)
                }
            }
        }
        stage(&apos;Trigger Gametool Build job&apos;) {
            steps {
                script {
                    List&lt;JobReference&gt; jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def codeChangelist = env.CODE_CHANGELIST
                        def cleanLocal = params.CLEAN_LOCAL
                        List&lt;String&gt; gametoolsToBuild = (List&lt;String&gt;) Eval.me(env.GAMETOOLS_TO_BUILD)

                        def args = [
                            string(name: &apos;CODE_CHANGELIST&apos;, value: codeChangelist),
                            booleanParam(name: &apos;CLEAN_LOCAL&apos;, value: cleanLocal),
                        ]

                        def injectMap = [
                            &apos;code_changelist&apos;: codeChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = &quot;${env.JOB_NAME}.${codeChangelist}&quot;
                        Map jobs = [:]
                        gametoolsToBuild.each { String gametool -&gt;
                            jobs[gametool] = {
                                String jobName = &quot;${env.BRANCH_NAME}.gametool.${gametool}&quot;
                                def downstreamJob = build(job: jobName, parameters: args, propagate: false)
                                jobReferences &lt;&lt; new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: false)
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        parallel(jobs)

                        def slackSettings = branchInfo.gametool_settings.slack_channel
                        SlackMessageNew(currentBuild, slackSettings, project.short_name)
                    }
                }
            }
        }
    }
}

String toString() {
    return &apos;scripts.schedulers.gametool.GametoolScheduler&apos;
}
</script>
    <sandbox>true</sandbox>
  </definition>
  <triggers/>
  <quietPeriod>0</quietPeriod>
  <disabled>false</disabled>
</flow-definition>