<?xml version="1.1" encoding="UTF-8"?>
<hudson.plugins.sectioned__view.SectionedView plugin="sectioned-view@1.30">
  <name>dev-na-to-trunk-sub Autotests</name>
  <filterExecutors>false</filterExecutors>
  <filterQueue>false</filterQueue>
  <properties class="hudson.model.View$PropertyList"/>
  <sections>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Orchestration</name>
      <includeRegex>(dev-na-to-trunk-sub)\.autotest.*\.start</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
        <hudson.views.BuildButtonColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Utility jobs</name>
      <includeRegex>dev-na-to-trunk-sub.(build.selector|bilbo.register-.*-autotestutils|autotest-to-integration.code)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.frostedtests.*(job).*)|(dev-na-to-trunk-sub.frostedtests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests_extended</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.frostedtests_extended.*(job).*)|(dev-na-to-trunk-sub.frostedtests_extended.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>unittests</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.unittests.*(job).*)|(dev-na-to-trunk-sub.unittests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>unittests_engine</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.unittests_engine.*(job).*)|(dev-na-to-trunk-sub.unittests_engine.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_playtest</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_func_playtest.*(job).*)|(dev-na-to-trunk-sub.pt_func_playtest.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.lkg_qv.*(job).*)|(dev-na-to-trunk-sub.lkg_qv.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests_fbapi</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.frostedtests_fbapi.*(job).*)|(dev-na-to-trunk-sub.frostedtests_fbapi.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_auto</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.lkg_auto.*(job).*)|(dev-na-to-trunk-sub.lkg_auto.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_bootanddeploy</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.lkg_bootanddeploy.*(job).*)|(dev-na-to-trunk-sub.lkg_bootanddeploy.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_bootanddeploy_tool</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.lkg_bootanddeploy_tool.*(job).*)|(dev-na-to-trunk-sub.lkg_bootanddeploy_tool.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_checkmate</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.lkg_checkmate.*(job).*)|(dev-na-to-trunk-sub.lkg_checkmate.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_performance_setup.*(job).*)|(dev-na-to-trunk-sub.pt_perf_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_highpriority_performance_setup.*(job).*)|(dev-na-to-trunk-sub.pt_perf_highpriority_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup_win</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_highpriority_performance_setup_win.*(job).*)|(dev-na-to-trunk-sub.pt_perf_highpriority_performance_setup_win.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_flythroughs_final_setup</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_highpriority_flythroughs_final_setup.*(job).*)|(dev-na-to-trunk-sub.pt_perf_highpriority_flythroughs_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>performance_minspec_final</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.performance_minspec_final.*(job).*)|(dev-na-to-trunk-sub.performance_minspec_final.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_minspec_performance_setup</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_minspec_performance_setup.*(job).*)|(dev-na-to-trunk-sub.pt_perf_minspec_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_lowpriority_final_setup.*(job).*)|(dev-na-to-trunk-sub.pt_perf_lowpriority_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_synced</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_lowpriority_final_setup_synced.*(job).*)|(dev-na-to-trunk-sub.pt_perf_lowpriority_final_setup_synced.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_synced_ps5</name>
      <includeRegex>((dev-na-to-trunk-sub)\.autotest.*.*\.pt_perf_lowpriority_final_setup_synced_ps5.*(job).*)|(dev-na-to-trunk-sub.pt_perf_lowpriority_final_setup_synced_ps5.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
  </sections>
</hudson.plugins.sectioned__view.SectionedView>