package com.ea.project.bctch1.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.jobs.LibCustomScript
import com.ea.lib.jobsettings.ShiftSettings
import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.bctch1.BctCh1

class CH1_content_dev {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                                : project.dataset,
        elipy_call                             : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call                     : project.elipy_install_call,
        frostbite_licensee                     : project.frostbite_licensee,
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : project.azure_elipy_call,
        azure_elipy_install_call               : project.azure_elipy_install_call,
        azure_workspace_root                   : project.azure_workspace_root,
        azure_fileshare                        : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'glacier_azure_fileshare',
            target_build_share         : 'bfglacier',
        ],
        job_label_statebuild                   : 'statebuild',
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [
                    config        : 'release',
                    framework_args: ['-G:frostbite.use-prebuilt-native-binaries=true'],
                ],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
                (LibPerforce.GAMETOOL_FROSTYISOTOOL)              : [],
                (LibPerforce.GAMETOOL_DRONE)                      : [],
                (LibPerforce.GAMETOOL_FRAMEWORK)                  : [],
                (LibPerforce.GAMETOOL_FBENV)                      : [],
            ],
        ],
        coverity_settings                      : [
            credentials            : 'monkey.bct',
            ess_secrets_credential : 'bct-secrets-secret-id',
            ess_secrets_key        : 'BCT_SECRETS_SECRET_ID',
            run_coverity           : true,
            trigger                : '@daily',
            job_label              : 'ch1_content_dev_coverity',
            artifactory_source_path: 'bct-infrax-generic-fed/tools/coverity/cov-analysis-win64-2024.12.0.zip',
            p4_code_creds          : 'bct-la-p4',
            p4_code_server         : 'dicela-p4edge-fb.la.ad.ea.com:2001'
        ],
        autotest_remote_settings               : [
            eala     : [
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                p4_data_creds : 'bct-la-p4',
                p4_data_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            ],
            dice     : [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield01',
                p4_data_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ],
            criterion: [
                p4_code_creds : 'perforce-battlefield-criterion',
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield-criterion',
                p4_data_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
            ]
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: 'H 0 * * *',
            referenceJob: '.data.start',
            label: 'pipeline_determinism',
            timeoutHours: 24,
        ),
        custom_script                          : [
            (LibCustomScript.PORTAL_MAKE_SDK.jobName): new CustomScriptConfiguration(
                scriptPath: LibCustomScript.PORTAL_MAKE_SDK.scriptPath,
                executable: LibCustomScript.PORTAL_MAKE_SDK.executable,
                executableArgs: LibCustomScript.PORTAL_MAKE_SDK.executableArgs,
                defaultScriptArgs: LibCustomScript.PORTAL_MAKE_SDK.defaultScriptArgs,
                label: 'statebuild',
                jobName: LibCustomScript.PORTAL_MAKE_SDK.jobName,
            ),
        ],
    ]
    static Map code_settings = [
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify', '#bf-ch1-sp-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
        statebuild_code_nomaster     : false,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        content_layers               : ['C1S2B1'],
        deployment_data_branch       : true,
        deployment_data_reference_job: 'CH1-content-dev.data.start',
        enable_daily_data_clean      : true,
        enable_lkg_cleaning          : true,
        slack_channel_data           : [
            channels                  : ['#bct-build-notify', '#bf-ch1-sp-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data              : false,
        statebuild_webexport         : false,
        timeout_hours_data           : 10,
        timeout_hours_enlighten_job  : 12,
        timeout_hours_upgrade_data   : 8,
        webexport_allow_failure      : true,
        webexport_branch             : true,
    ]
    static Map frosty_settings = [
        frosty_reference_job          : 'CH1-content-dev.deployment-data.start',
        enable_eac_win64_digital      : true,
        enable_eac_win64_steam        : true,
        enable_eac_win64_combine      : true,
        enable_eac_win64_steam_combine: true,
        parallel_limit_frosty         : 2,
        slack_channel_frosty          : [
            channels                  : ['#bct-build-notify', '#bf-ch1-sp-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty             : false,
        timeout_hours_frosty          : 6,
        use_linuxclient               : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                              : 'DevLevels',
        bilbo_store_offsite                : true,
        clean_data_validation_pipeline_args: ' --disable-caches true',
        combine_bundles                    : [
            combine_asset        : 'CombinedShippingMPLevels',
            combine_reference_job: 'CH1-SP-content-dev.deployment-data.start',
            is_target_branch     : true,
            source_branch_code   : 'CH1-SP-content-dev',
            source_branch_data   : 'CH1-SP-content-dev',
        ],
        custom_tests                       : [
            custom_configs: [
                'online-integration-tests.json',
                'online-integration-tests-ade.json',
                'online-integration-tests-gsintegration.json',
                'online-integration-tests-gsintegration-event.json',
            ],
        ],
        drone_outsourcers                  : ['Jukebox'],
        enable_clean_build_validation      : false,
        enable_lkg_p4_counters             : true,
        extra_data_args                    : ['--pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args                  : ['--pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        import_avalanche_autotest          : false,
        linux_docker_images                : false,
        move_location_parallel             : true,
        new_locations                      : [
            earo        : [
                elipy_call_new_location: project.elipy_call_earo + ' --use-fbenv-core',
            ],
            Guildford   : [
                elipy_call_new_location: project.elipy_call_criterion + ' --use-fbenv-core',
            ],
            Montreal    : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        offsite_basic_drone_zip_builds     : true,
        offsite_drone_basic_builds         : true,
        quickscope_db                      : 'kinpipeline',
        quickscope_import                  : true,
        remote_masters_to_receive_code     : [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        remote_masters_to_receive_data     : [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        reshift_offsitedrone               : true,
        server_asset                       : 'Game/Setup/Build/DevMPLevels',
        shift_branch                       : true,
        shift_reference_job                : 'CH1-content-dev.frosty.start',
        shift_upload_timeout_hours         : 10,
        single_stream_smoke                : true,
        skip_icepick_settings_file         : true,
        slack_channel_shift                : [
            channels                  : ['#bf-ch1-sp-notify'],
            skip_for_multiple_failures: true,
        ],
        strip_symbols                      : false,
        timeout_hours_clean_data_validation: 20,
        trigger_string_shift               : 'TZ=Europe/Stockholm \n H 1,4,7,13,19  * * 1-6\nH 8,14,20 * * 7',
        trigger_string_shift_offsite_drone : 'TZ=Europe/London \n H 16 * * 1-6\nH 16 * * 7',
        trigger_type_shift                 : 'cron',
        upgrade_data_job                   : true,
        use_deprecated_blox_packages       : true,
        register_smoke_multi_location      : true,
        oreans_protection                  : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [
        codepreflight_reference_job      : 'CH1-content-dev.code.lastknowngood',
        concurrent_code                  : 1,
        concurrent_data                  : 2,
        datapreflight_reference_job      : 'CH1-content-dev.data.lastknowngood',
        enable_custom_cl                 : true,
        extra_codepreflight_args         : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml --framework-args -D:eaconfig.optimization.ltcg=off --framework-args -G:eaconfig.enablesndbs=true",
        extra_datapreflight_args         : ' --pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ',
        force_rebuild_preflight          : true,
        pre_preflight                    : true,
        prepreflight_idle_length         : '60', //minutes idling before to run pre-preflight.
        slack_channel_preflight          : [channels: ['#cobra-build-preflight']],
        statebuild_codepreflight         : false,
        statebuild_datapreflight         : false,
        timeout_hours_datapreflight      : 12,
        trigger_string_pre_preflight_data: 'H * * * 1-5\nH 6-23 * * 6-7',
        trigger_type                     : 'cron',
        use_icepick_test                 : true,
        validate_direct_references       : true,
        content_layers_preflight         : ['C1S2B1', 'source'],
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true], [name: 'deprecation-test', allow_failure: true, compile_unit_tests: true]]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_stressbulkbuild_matrix = [
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.bfdata.upgrade.data', args: ['code_changelist'], trigger_only_on_new_code: true],
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
        // [name: '.register.smoke', args: ['code_changelist']],
    ]
    static List data_matrix = [
        [
            name                : 'win64',
            enlighten_bake_group: [
                [
                    group_name    : 'group_1',
                    cron_string   : 'H 13 * * 1-5',
                    enlighten_bake: [
                        [
                            name  : 'TestRange_ContentsLookDev_Gym',
                            asset : 'Test/TestRanges/TestRange_ContentsLookDev_Gym/TestRange_ContentsLookDev_Gym',
                            mode  : 'submit',
                            filter: '[062d6b23-7d7a-46cf-8754-8173a05f975f]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_MP_BrooklynVTAR_HighEnd',
                            asset : 'Test/TestRanges/Gym_EUS_Brooklyn_VTAR/Gym_EUS_Brooklyn_VTAR',
                            mode  : 'submit',
                            filter: '[bab487ba-6398-4995-bc8d-3948be2f1cb0]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_SP_Prologue_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Prologue/DSUB_SP_Prologue',
                            mode  : 'submit',
                            filter: '[31e648ac-8bf9-4d1a-b505-1c0f7c6a3e9a]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_SP_Prologue_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Prologue/DSUB_SP_Prologue',
                            mode  : 'submit',
                            filter: '[4ca79328-1eb6-4bee-8056-dfd226766d8a]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_SP_TankAssault_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_TankAssault/DSUB_SP_TankAssault',
                            mode  : 'submit',
                            filter: '[02097fc9-fcc7-48ca-9ed5-702e70ffb9e6]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_SP_TankAssault_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_TankAssault/DSUB_SP_TankAssault',
                            mode  : 'submit',
                            filter: '[76688f30-4677-4154-a834-1d2dd061e1a0]',
                            type  : 'asset'
                        ],
                        [
                            name : 'ZS_GlobalPrecomputeDesc_MP_Granite_01',
                            asset: 'Game/GlacierGranite/Levels/MP_Granite/ZS_GlobalPrecomputeDesc_MP_Granite_01',
                            mode : 'submit',
                            type : 'ZoneStreamer'
                        ],
                        // [
                        //     name  : 'MP_Contaminated',
                        //     asset : 'Game/GlacierMP/Levels/MP_Contaminated/MP_Contaminated',
                        //     mode  : 'submit',
                        //     filter: '[ebf6c1f4-c8a1-4fdc-8798-270453e2aac4]',
                        //     type  : 'asset'
                        // ],
                    ]
                ],
                [
                    group_name    : 'group_2',
                    cron_string   : 'H 2 * * 1-5',
                    enlighten_bake: [
                        [
                            name  : 'Enlighten_MP_Abbasid_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Abbasid/MP_Abbasid',
                            mode  : 'submit',
                            filter: '[e1845bb6-373d-42aa-8235-67e76ef6be5a]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_MP_Abbasid_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Abbasid/MP_Abbasid',
                            mode  : 'submit',
                            filter: '[62606c4d-e6a7-4698-8767-7ceb1720c104]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Tungsten_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Tungsten/MP_Tungsten',
                            mode  : 'submit',
                            filter: '[fc23b954-f8f3-4967-9de3-fcbe797b6ac1]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Tungsten_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Tungsten/MP_Tungsten',
                            mode  : 'submit',
                            filter: '[b0977be4-8966-4bcb-90c1-94c6da164a8d]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'DSUB_SP_NightRaid_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Nightraid/DSUB_SP_Nightraid',
                            mode  : 'submit',
                            filter: '[f4e389e1-dd37-4e91-9ed1-be4efd063d30]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'DSUB_SP_NightRaid_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Nightraid/DSUB_SP_Nightraid',
                            mode  : 'submit',
                            filter: '[316e9104-6b68-4b98-b5b3-e56ae12154e6]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_BrooklynProtect_Enlighten_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_BrooklynProtect/DSUB_SP_BrooklynProtect',
                            mode  : 'submit',
                            filter: '[6696e0a5-3334-4116-95b5-9f4dfe50111f]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_BrooklynProtect_Enlighten_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_BrooklynProtect/DSUB_SP_BrooklynProtect',
                            mode  : 'submit',
                            filter: '[14520c72-9b92-4181-8f44-d85641554d03]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'DSUB_SP_Assault_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Assault/DSUB_SP_Assault',
                            mode  : 'submit',
                            filter: '[953eea3c-a2db-4bdb-9b5a-c70d2a9a4338]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'DSUB_SP_Assault_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Assault/DSUB_SP_Assault',
                            mode  : 'submit',
                            filter: '[127a6359-d98a-497a-86ce-5008edac3bd4]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_MP_Dumbo_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Dumbo/MP_Dumbo',
                            mode  : 'submit',
                            filter: '[ca1a51f1-fe77-4184-9132-f2e642232483]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_MP_Dumbo_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Dumbo/MP_Dumbo',
                            mode  : 'submit',
                            filter: '[91e73452-8c1a-49cc-b39f-72c5dcdcaf73]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_MP_Battery_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Battery/MP_Battery',
                            mode  : 'submit',
                            filter: '[7a198a3c-7964-41c9-ab7b-732a7564fd14]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Enlighten_MP_Battery_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Battery/MP_Battery',
                            mode  : 'submit',
                            filter: '[514426d3-a2ed-423f-a346-29d1956ee6a7]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'Diorama_SP_NightRaid',
                            asset : 'Game/Dev/Dioramas/SP/Diorama_SP_NightRaid/Diorama_SP_NightRaid',
                            mode  : 'submit',
                            filter: '[371d2406-032c-486e-b3ce-e581e3a5f1ec]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_BrooklynAttack_Enlighten_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_BrooklynAttack/DSUB_SP_BrooklynAttack',
                            mode  : 'submit',
                            filter: '[1e84efe6-e8fa-433d-902d-0555f257205d]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_BrooklynAttack_Enlighten_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_BrooklynAttack/DSUB_SP_BrooklynAttack',
                            mode  : 'submit',
                            filter: '[6eade36a-f638-4a32-ac79-d945f518afa9]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Aftermath_Enlighten_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Aftermath/MP_Aftermath',
                            mode  : 'submit',
                            filter: '[50b3afc3-89b3-4ebe-a158-e29ca837fad4]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Aftermath_Enlighten_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Aftermath/MP_Aftermath',
                            mode  : 'submit',
                            filter: '[83fddc93-3186-4db8-8581-c0229911dff7]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Dumbo',
                            asset : 'Game/GlacierMP/Levels/MP_Dumbo/MP_Dumbo',
                            mode  : 'submit',
                            filter: '[65c5bd72-702c-4a1b-b6eb-eddc34cb9f70]',
                            type  : 'asset'
                        ],
                        // [
                        //     name  : 'SP_Infiltration',
                        //     asset : 'Game/GlacierSP/Levels/DSUB_SP_Infiltration/DSUB_SP_Infiltration',
                        //     mode  : 'submit',
                        //     filter: '[07e11afc-415d-4187-8cb1-8f035fb10c8a]',
                        //     type  : 'asset'
                        // ],
                        [
                            name  : 'SP_Drone_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Drone/DSUB_SP_Drone',
                            mode  : 'submit',
                            filter: '[c0733c1f-4ce3-4b6c-b91f-f0e15a779721]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_Infiltration_HighEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Infiltration/DSUB_SP_Infiltration',
                            mode  : 'submit',
                            filter: '[5587c8c3-e7c4-4a76-a3bf-2c27d2123eb5]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_Infiltration_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Infiltration/DSUB_SP_Infiltration',
                            mode  : 'submit',
                            filter: '[cad58882-5cb4-4857-84df-2db6813668a0]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'SP_Drone_LowEnd',
                            asset : 'Game/GlacierSP/Levels/DSUB_SP_Drone/DSUB_SP_Drone',
                            mode  : 'submit',
                            filter: '[0fce117f-f846-40c0-8112-a0e222e10d89]',
                            type  : 'asset'
                        ],
                        // [
                        //     name  : 'MP_Propaganda',
                        //     asset : 'Level: Game/GlacierMP/Levels/MP_Propaganda/MP_Propaganda',
                        //     mode  : 'submit',
                        //     filter: '[e7a6bd92-3652-4c1d-ab38-f0f4e4dc1fdb]',
                        //     type  : 'asset'
                        // ],
                        [
                            name  : 'MP_FireStorm_Highend',
                            asset : 'Game/GlacierMP/Levels/MP_FireStorm/MP_FireStorm',
                            mode  : 'submit',
                            filter: '[9b21314b-332d-423f-b1d5-9c1cd6fa6f89]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_FireStorm_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_FireStorm/MP_FireStorm',
                            mode  : 'submit',
                            filter: '[17035908-5ec3-4fc5-bc81-00afccfb7aea]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Capstone_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Capstone/MP_Capstone',
                            mode  : 'submit',
                            filter: '[9d4e5662-b2de-41b7-8721-25febc96018e]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Capstone_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Capstone/MP_Capstone',
                            mode  : 'submit',
                            filter: '[ee4b1ced-b2fb-44c7-a288-9007d2f63cc6]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Limestone_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Limestone/MP_Limestone',
                            mode  : 'submit',
                            filter: '[13127704-7a3f-4556-9c9b-db06b2b73bd8]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Limestone_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Limestone/MP_Limestone',
                            mode  : 'submit',
                            filter: '[a2de539d-93bb-4b4c-a674-484fdeb43c1e]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Outskirts_HighEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Outskirts/MP_Outskirts',
                            mode  : 'submit',
                            filter: '[b7561ebb-c00d-47bd-bc2e-5810d24bdfd0]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'MP_Outskirts_LowEnd',
                            asset : 'Game/GlacierMP/Levels/MP_Outskirts/MP_Outskirts',
                            mode  : 'submit',
                            filter: '[30df8f1f-93ec-4920-b019-f6e9c1b21306]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'DSUB_SP_Invasion_HighEnd',
                            asset : 'Game/GlacierSP/Levels/SP_Invasion/DSUB_SP_Invasion',
                            mode  : 'submit',
                            filter: '[c1e45036-3387-4409-b051-3bc8bd826e83]',
                            type  : 'asset'
                        ],
                        [
                            name  : 'DSUB_SP_Invasion_LowEnd',
                            asset : 'Game/GlacierSP/Levels/SP_Invasion/DSUB_SP_Invasion',
                            mode  : 'submit',
                            filter: '[f1335152-f35e-4244-8d08-42265a9cad20]',
                            type  : 'asset'
                        ],
                    ]
                ],
            ]
        ],
        [name: 'ps5'],
        [name: 'xbsx'],
        [name: 'server', combine_platform: false],
        [name: 'linux64', combine_platform: false],
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.copy-integrate-to.CH1-SP-content-dev.start', args: []],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
        [name: '.code.win64game.final.copy-build-to-azure', args: ['code_changelist']],
        [name: '.code.win64server.final.copy-build-to-azure', args: ['code_changelist']],
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.deployment-data.start', args: []],
        [name: '.frosty.start', args: []],
        [name: 'CH1-content-dev-C1S2B1.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance'],
                                   [format: 'files', config: 'performance', region: 'ww', args: ''],
                                   [format: 'files', config: 'release', region: 'ww', args: ''],
                                   [format: 'steam_combine', config: 'final', region: 'ww', args: '', allow_failure: true],
                                   [format: 'steam_combine', config: 'retail', region: 'ww', args: '', allow_failure: true],
                                   [format: 'combine', config: 'final', region: 'ww', args: '', allow_failure: true],
                                   [format: 'combine', config: 'retail', region: 'ww', args: '', allow_failure: true]]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: ''],
                                 [format: 'combine', config: 'final', region: 'ww', args: '', allow_failure: true],
                                 [format: 'combine', config: 'retail', region: 'ww', args: '', allow_failure: true]]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: ''],
                                  [format: 'combine', config: 'final', region: 'ww', args: '', allow_failure: true],
                                  [format: 'combine', config: 'retail', region: 'ww', args: '', allow_failure: true]]],
        [name: 'server', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                    [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
        [name: '.win64.upload_to_steam.combine.ww.final', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
        [name: '.win64.upload_to_steam.combine.ww.retail', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['final']],
        [name: 'xbsx', configs: ['performance']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
    ]
    static List data_preflight_matrix = [
        [name: 'win64', platform: 'win64', assets: ['PreflightLevels'], extra_label: ''],
        [name: 'server', platform: 'server', assets: ['PreflightLevels'], extra_label: ''],
    ]
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww']]],
        [name: 'mod-level-tools', variants: [[format: 'na', config: 'na', region: 'na']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = [
        [name: '.spin.linux64.files.final.ww', args: ['code_changelist', 'data_changelist']],
        [name: 'CH1-playtest-sp.frosty.start', args: ['code_changelist', 'data_changelist']],
    ]
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register.local', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
        [upstream_job: '.bilbo.register.local', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_DRONE}.start", args: ['code_changelist']],
    ]
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']],
        [platform: 'win64game', content_type: ['code'], config: ['final']],
        [platform: 'win64server', content_type: ['code'], config: ['final']],
    ]
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64', job_label: 'CH1-content-dev && pipeline_determinism && win64'],
        // [platform: 'win64', job_name: 'debugdb', job_label: 'CH1-content-dev && pipeline_determinism && debugdb' additional_script_args: '-add_cook_args=\"-forceDebugTarget -Pipeline.MaxConcurrencyThrottleMemoryThreshold 0.8\"'],
        // [platform: 'ps5', job_label: 'CH1-content-dev && pipeline_determinism && ps5', additional_script_args: '-add_cook_args=\"-Pipeline.MaxConcurrencyThrottleMemoryThreshold 0.8\"'],
        // [platform: 'xbsx', job_label: 'CH1-content-dev && pipeline_determinism && xbsx', additional_script_args: '-add_cook_args=\"-Pipeline.MaxConcurrencyThrottleMemoryThreshold 0.8\"'],
    ]
}
