<?xml version="1.1" encoding="UTF-8"?>
<hudson.plugins.sectioned__view.SectionedView plugin="sectioned-view@1.30">
  <name>CH1-content-dev Autotests</name>
  <filterExecutors>false</filterExecutors>
  <filterQueue>false</filterQueue>
  <properties class="hudson.model.View$PropertyList"/>
  <sections>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Orchestration</name>
      <includeRegex>(CH1-content-dev)\.autotest.*\.start</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
        <hudson.views.BuildButtonColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Utility jobs</name>
      <includeRegex>CH1-content-dev.(build.selector|bilbo.register-.*-autotestutils|autotest-to-integration.code)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_auto</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_auto.*(job).*)|(CH1-content-dev.lkg_auto.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_auto_la_test</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_auto_la_test.*(job).*)|(CH1-content-dev.lkg_auto_la_test.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_bootanddeploy_ScratchDisk</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_bootanddeploy_ScratchDisk.*(job).*)|(CH1-content-dev.lkg_bootanddeploy_ScratchDisk.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_bootanddeploy_tool</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_bootanddeploy_tool.*(job).*)|(CH1-content-dev.lkg_bootanddeploy_tool.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>LKG_AudioTestSuite</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.LKG_AudioTestSuite.*(job).*)|(CH1-content-dev.LKG_AudioTestSuite.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_playtest</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_playtest.*(job).*)|(CH1-content-dev.pt_func_playtest.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_crossplay</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_crossplay.*(job).*)|(CH1-content-dev.pt_crossplay.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_crossplay_spin</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_crossplay_spin.*(job).*)|(CH1-content-dev.pt_crossplay_spin.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.frostedtests.*(job).*)|(CH1-content-dev.frostedtests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_qv.*(job).*)|(CH1-content-dev.lkg_qv.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv_la</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_qv_la.*(job).*)|(CH1-content-dev.lkg_qv_la.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_checkmate</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_checkmate.*(job).*)|(CH1-content-dev.lkg_checkmate.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_final_setup.*(job).*)|(CH1-content-dev.pt_perf_highpriority_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_highpriority_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup_two</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_performance_setup_two.*(job).*)|(CH1-content-dev.pt_perf_highpriority_performance_setup_two.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup_spin_server</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_performance_setup_spin_server.*(job).*)|(CH1-content-dev.pt_perf_highpriority_performance_setup_spin_server.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup_ch1_content_validation</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_setup_ch1_content_validation.*(job).*)|(CH1-content-dev.pt_perf_performance_setup_ch1_content_validation.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup_trinity</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_setup_trinity.*(job).*)|(CH1-content-dev.pt_perf_performance_setup_trinity.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_meta_setup_trinity</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_meta_setup_trinity.*(job).*)|(CH1-content-dev.pt_perf_performance_meta_setup_trinity.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup_trinity_two</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_setup_trinity_two.*(job).*)|(CH1-content-dev.pt_perf_performance_setup_trinity_two.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup_trinity_release</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_setup_trinity_release.*(job).*)|(CH1-content-dev.pt_perf_performance_setup_trinity_release.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup_trinity_detailed</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_setup_trinity_detailed.*(job).*)|(CH1-content-dev.pt_perf_performance_setup_trinity_detailed.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup_win</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_performance_setup_win.*(job).*)|(CH1-content-dev.pt_perf_highpriority_performance_setup_win.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_meta_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_meta_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_meta_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_f2p_highpriority_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_f2p_highpriority_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_f2p_highpriority_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_f2p_highpriority_performance_setup_detailed</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_f2p_highpriority_performance_setup_detailed.*(job).*)|(CH1-content-dev.pt_perf_f2p_highpriority_performance_setup_detailed.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_flythroughs_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_flythroughs_final_setup.*(job).*)|(CH1-content-dev.pt_perf_highpriority_flythroughs_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_HUD</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_HUD.*(job).*)|(CH1-content-dev.pt_perf_HUD.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_HUD_MinSpec</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_HUD_MinSpec.*(job).*)|(CH1-content-dev.pt_perf_HUD_MinSpec.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_HUD_RecSpec</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_HUD_RecSpec.*(job).*)|(CH1-content-dev.pt_perf_HUD_RecSpec.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_HUD_Trinity</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_HUD_Trinity.*(job).*)|(CH1-content-dev.pt_perf_HUD_Trinity.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_checkmate_la</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_checkmate_la.*(job).*)|(CH1-content-dev.lkg_checkmate_la.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_HUD_XBSS</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_HUD_XBSS.*(job).*)|(CH1-content-dev.pt_perf_HUD_XBSS.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_flythroughs_final_setup2</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_flythroughs_final_setup2.*(job).*)|(CH1-content-dev.pt_perf_highpriority_flythroughs_final_setup2.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_final_implicit_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_final_implicit_setup.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_final_implicit_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_flythroughs_final_setup_mem</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_flythroughs_final_setup_mem.*(job).*)|(CH1-content-dev.pt_perf_highpriority_flythroughs_final_setup_mem.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_flythroughs_final_setup_mem_xbss</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_flythroughs_final_setup_mem_xbss.*(job).*)|(CH1-content-dev.pt_perf_highpriority_flythroughs_final_setup_mem_xbss.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_final_setup.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_final_setup_trinity</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_final_setup_trinity.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_final_setup_trinity.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_XBSS_CH1_code_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_XBSS_CH1_code_final_setup.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_XBSS_CH1_code_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_XBSS_CH1_code_final_setup2</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_XBSS_CH1_code_final_setup2.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_XBSS_CH1_code_final_setup2.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_xbss_sp_final</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_xbss_sp_final.*(job).*)|(CH1-content-dev.pt_perf_xbss_sp_final.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_XBSS_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_XBSS_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_XBSS_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_XBSS_performance_setup_two</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_XBSS_performance_setup_two.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_XBSS_performance_setup_two.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_XBSS_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_XBSS_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_XBSS_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_XBSS_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_XBSS_final_setup.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_XBSS_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_minspec_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_minspec_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_minspec_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_minspec_meta_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_minspec_meta_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_minspec_meta_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_high_minspec_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_high_minspec_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_high_minspec_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_setup_ch1_stage_minspec_eala</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_setup_ch1_stage_minspec_eala.*(job).*)|(CH1-content-dev.pt_perf_setup_ch1_stage_minspec_eala.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_minspec_final_setup2</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_minspec_final_setup2.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_minspec_final_setup2.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_minspec_sp_final</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_minspec_sp_final.*(job).*)|(CH1-content-dev.pt_perf_minspec_sp_final.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_recspec_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_high_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_high_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_recspec_high_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_meta_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_meta_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_recspec_meta_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_recspec_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_recspec_final_setup.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_recspec_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_minspec_final_setup3</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_minspec_final_setup3.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_minspec_final_setup3.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_minspec_final_setup_ch1_code</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_flythroughs_minspec_final_setup_ch1_code.*(job).*)|(CH1-content-dev.pt_perf_flythroughs_minspec_final_setup_ch1_code.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_minspec</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_minspec.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_minspec.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_Trinity</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_Trinity.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_Trinity.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_recspec</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_recspec.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_recspec.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_xbss</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_xbss.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_xbss.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_CH1</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_CH1.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_CH1.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_synced</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_synced.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_synced.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_synced_xbss</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_synced_xbss.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_synced_xbss.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_synced_ps5</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_synced_ps5.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_synced_ps5.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_performance_setup2</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_performance_setup2.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_performance_setup2.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup_la</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_performance_setup_la.*(job).*)|(CH1-content-dev.pt_perf_highpriority_performance_setup_la.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_lowpriority_final_setup_la</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_lowpriority_final_setup_la.*(job).*)|(CH1-content-dev.pt_perf_lowpriority_final_setup_la.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>large_scale_autoplaytest_setup_ch1_content_dev</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.large_scale_autoplaytest_setup_ch1_content_dev.*(job).*)|(CH1-content-dev.large_scale_autoplaytest_setup_ch1_content_dev.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>persistence_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.persistence_final_setup.*(job).*)|(CH1-content-dev.persistence_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>large_scale_autoplaytest_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.large_scale_autoplaytest_setup.*(job).*)|(CH1-content-dev.large_scale_autoplaytest_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>large_scale_autoplaytest_setup_fullbox</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.large_scale_autoplaytest_setup_fullbox.*(job).*)|(CH1-content-dev.large_scale_autoplaytest_setup_fullbox.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>large_scale_autoplaytest_setup_online_mp</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.large_scale_autoplaytest_setup_online_mp.*(job).*)|(CH1-content-dev.large_scale_autoplaytest_setup_online_mp.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>autoplaytest_setup_online_mp_ThinClient</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.autoplaytest_setup_online_mp_ThinClient.*(job).*)|(CH1-content-dev.autoplaytest_setup_online_mp_ThinClient.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>large_scale_autoplaytest_setup_online_two_mp</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.large_scale_autoplaytest_setup_online_two_mp.*(job).*)|(CH1-content-dev.large_scale_autoplaytest_setup_online_two_mp.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>thin_client_linux_server_synced</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.thin_client_linux_server_synced.*(job).*)|(CH1-content-dev.thin_client_linux_server_synced.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>multi_platform_client_spin_server_test</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.multi_platform_client_spin_server_test.*(job).*)|(CH1-content-dev.multi_platform_client_spin_server_test.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_UltSpec_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_UltSpec_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_highpriority_UltSpec_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_minspec_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_minspec_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_highpriority_minspec_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_minspec_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_highpriority_minspec_final_setup.*(job).*)|(CH1-content-dev.pt_perf_highpriority_minspec_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_highpriority_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_highpriority_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_recspec_highpriority_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_minspec_sp_perf_performance</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_minspec_sp_perf_performance.*(job).*)|(CH1-content-dev.pt_perf_minspec_sp_perf_performance.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_sp_perf_performance_FirstBatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_sp_perf_performance_FirstBatch.*(job).*)|(CH1-content-dev.pt_perf_sp_perf_performance_FirstBatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_sp_perf_performance_SecondBatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_sp_perf_performance_SecondBatch.*(job).*)|(CH1-content-dev.pt_perf_sp_perf_performance_SecondBatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_trinity_sp_perf_performance</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_trinity_sp_perf_performance.*(job).*)|(CH1-content-dev.pt_perf_trinity_sp_perf_performance.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_xbss_sp_perf_performance</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_xbss_sp_perf_performance.*(job).*)|(CH1-content-dev.pt_perf_xbss_sp_perf_performance.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_xbss_meta_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_xbss_meta_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_xbss_meta_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_highpriority_performance_setup_2</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_highpriority_performance_setup_2.*(job).*)|(CH1-content-dev.pt_perf_recspec_highpriority_performance_setup_2.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_highpriority_final_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_highpriority_final_setup.*(job).*)|(CH1-content-dev.pt_perf_recspec_highpriority_final_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_trinity_sp_final</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_trinity_sp_final.*(job).*)|(CH1-content-dev.pt_perf_trinity_sp_final.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_mutators</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_mutators.*(job).*)|(CH1-content-dev.pt_func_mutators.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>release_setup_aitests_DICEAI</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.release_setup_aitests_DICEAI.*(job).*)|(CH1-content-dev.release_setup_aitests_DICEAI.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests_workflow_frostbite</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.frostedtests_workflow_frostbite.*(job).*)|(CH1-content-dev.frostedtests_workflow_frostbite.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests_workflow_battlefield</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.frostedtests_workflow_battlefield.*(job).*)|(CH1-content-dev.frostedtests_workflow_battlefield.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frostedtests_extended</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.frostedtests_extended.*(job).*)|(CH1-content-dev.frostedtests_extended.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv_win</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_qv_win.*(job).*)|(CH1-content-dev.lkg_qv_win.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv_win_criterion</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_qv_win_criterion.*(job).*)|(CH1-content-dev.lkg_qv_win_criterion.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_stab</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_stab.*(job).*)|(CH1-content-dev.pt_stab.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_stab_soak_mp_ch1_code</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_stab_soak_mp_ch1_code.*(job).*)|(CH1-content-dev.pt_stab_soak_mp_ch1_code.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_checkmate_beta</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_checkmate_beta.*(job).*)|(CH1-content-dev.lkg_checkmate_beta.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_checkmate_alpha</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.lkg_checkmate_alpha.*(job).*)|(CH1-content-dev.lkg_checkmate_alpha.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>unittests</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.unittests.*(job).*)|(CH1-content-dev.unittests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>unittests_engine</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.unittests_engine.*(job).*)|(CH1-content-dev.unittests_engine.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_A2B_one</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_A2B_one.*(job).*)|(CH1-content-dev.pt_func_A2B_one.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_A2B_two</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_A2B_two.*(job).*)|(CH1-content-dev.pt_func_A2B_two.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_SP</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_SP.*(job).*)|(CH1-content-dev.pt_func_SP.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_CC</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_CC.*(job).*)|(CH1-content-dev.pt_func_CC.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_META</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_META.*(job).*)|(CH1-content-dev.pt_func_META.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_content</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_content.*(job).*)|(CH1-content-dev.pt_func_content.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_func_win64</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_func_win64.*(job).*)|(CH1-content-dev.pt_func_win64.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>releasetests</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.releasetests.*(job).*)|(CH1-content-dev.releasetests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>frosted_vstests</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.frosted_vstests.*(job).*)|(CH1-content-dev.frosted_vstests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_ultspec_sp_perf_performance_firstbatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_ultspec_sp_perf_performance_firstbatch.*(job).*)|(CH1-content-dev.pt_perf_ultspec_sp_perf_performance_firstbatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_ultspec_sp_perf_performance_secondbatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_ultspec_sp_perf_performance_secondbatch.*(job).*)|(CH1-content-dev.pt_perf_ultspec_sp_perf_performance_secondbatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_sp_perf_performance_firstbatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_sp_perf_performance_firstbatch.*(job).*)|(CH1-content-dev.pt_perf_recspec_sp_perf_performance_firstbatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_sp_perf_performance_secondbatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_sp_perf_performance_secondbatch.*(job).*)|(CH1-content-dev.pt_perf_recspec_sp_perf_performance_secondbatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_sp_final_FirstBatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_sp_final_FirstBatch.*(job).*)|(CH1-content-dev.pt_perf_recspec_sp_final_FirstBatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_sp_final_SecondBatch</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_recspec_sp_final_SecondBatch.*(job).*)|(CH1-content-dev.pt_perf_recspec_sp_final_SecondBatch.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_UltSpec_F2P_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_UltSpec_F2P_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_UltSpec_F2P_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_UltSpec_meta_performance_setup</name>
      <includeRegex>((CH1-content-dev)\.autotest.*.*\.pt_perf_UltSpec_meta_performance_setup.*(job).*)|(CH1-content-dev.pt_perf_UltSpec_meta_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
  </sections>
</hudson.plugins.sectioned__view.SectionedView>