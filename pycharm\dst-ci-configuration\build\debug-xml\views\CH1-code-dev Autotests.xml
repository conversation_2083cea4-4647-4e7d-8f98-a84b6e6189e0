<?xml version="1.1" encoding="UTF-8"?>
<hudson.plugins.sectioned__view.SectionedView plugin="sectioned-view@1.30">
  <name>CH1-code-dev Autotests</name>
  <filterExecutors>false</filterExecutors>
  <filterQueue>false</filterQueue>
  <properties class="hudson.model.View$PropertyList"/>
  <sections>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Orchestration</name>
      <includeRegex>(CH1-code-dev)\.autotest.*\.start</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
        <hudson.views.BuildButtonColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Utility jobs</name>
      <includeRegex>CH1-code-dev.(build.selector|bilbo.register-.*-autotestutils|autotest-to-integration.code)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
  </sections>
</hudson.plugins.sectioned__view.SectionedView>