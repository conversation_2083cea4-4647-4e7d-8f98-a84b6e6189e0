<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job@1505.vea_4b_20a_4a_495">
  <actions/>
  <description></description>
  <logRotator class="hudson.tasks.LogRotator">
    <daysToKeep>2</daysToKeep>
    <numToKeep>20</numToKeep>
    <artifactDaysToKeep>-1</artifactDaysToKeep>
    <artifactNumToKeep>-1</artifactNumToKeep>
    <removeLastBuild>false</removeLastBuild>
  </logRotator>
  <keepDependencies>false</keepDependencies>
  <properties>
    <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty/>
    <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty/>
    <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
      <triggers>
        <hudson.triggers.TimerTrigger>
          <spec>H/10 * * * 1-5</spec>
        </hudson.triggers.TimerTrigger>
      </triggers>
    </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    <com.sonyericsson.jenkins.plugins.bfa.model.ScannerJobProperty plugin="build-failure-analyzer@2.5.4">
      <doNotScan>false</doNotScan>
    </com.sonyericsson.jenkins.plugins.bfa.model.ScannerJobProperty>
  </properties>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition" plugin="workflow-cps@4018.vf02e01888da_f">
    <script>package scripts.schedulers

/**
 * syncSecretsAndConfigurations.groovy
 * This pipeline will sync secrets and JCasC configuration by cloning
 * container-images/jenkins repo, running the vault sync script and copying
 * jcasc files
 */

pipeline {
    agent { label &apos;master&apos; }

    options {
        timeout(time: 1, unit: &apos;HOURS&apos;)
    }

    stages {
        stage(&apos;Clone container-images/jenkins repo&apos;) {
            steps {
                git branch: &apos;master&apos;,
                    credentialsId: &apos;monkey-commons-ssh-v2&apos;,
                    url: &apos;*****************:dre-cobra/container-images/jenkins.git&apos;
            }
        }

        stage(&apos;Sync Vault&apos;) {
            steps {
                sh &apos;bash ./bootstrap-config/prepare_vault_jenkins.sh&apos;
            }
        }

        stage(&apos;Sync JCasC&apos;) {
            steps {
                sh &apos;bash ./bootstrap-config/copy_jcasc_files.sh&apos;
            }
        }

        stage(&apos;Apply Configurations&apos;) {
            steps {
                script {
                    if (env.JCASC_RELOAD_TOKEN) {
                        sh &apos;&apos;&apos;
                            set +x
                            curl --fail --silent --write-out &quot;Configurations reloaded successfully (HTTP %{http_code})&quot; \
                                 --request POST ${JENKINS_URL}/reload-configuration-as-code/?casc-reload-token=${JCASC_RELOAD_TOKEN}
                            &apos;&apos;&apos;
                    } else {
                        echo &quot;JCASC_RELOAD_TOKEN not found, Reload the configuration manually at ${JENKINS_URL}/manage/configuration-as-code/&quot;
                    }
                }
            }
        }
    }
}
</script>
    <sandbox>true</sandbox>
  </definition>
  <triggers/>
  <disabled>false</disabled>
</flow-definition>